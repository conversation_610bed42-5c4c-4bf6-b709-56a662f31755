import requests
from bs4 import Beautiful<PERSON>oup
import pandas as pd
import time
import json
from urllib.parse import urljoin, urlparse
import csv
from datetime import datetime
import os

class WebScraper:
    def __init__(self, delay=1, headers=None):
        """
        Initialize the web scraper

        Args:
            delay (int): Delay between requests in seconds
            headers (dict): Custom headers for requests
        """
        self.delay = delay
        self.session = requests.Session()

        # Default headers to avoid being blocked
        default_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        if headers:
            default_headers.update(headers)

        self.session.headers.update(default_headers)

    def get_page(self, url):
        """
        Get a webpage with error handling

        Args:
            url (str): URL to scrape

        Returns:
            BeautifulSoup object or None if failed
        """
        try:
            print(f"Fetching: {url}")
            response = self.session.get(url)
            response.raise_for_status()

            # Add delay to be respectful
            time.sleep(self.delay)

            return BeautifulSoup(response.content, 'html.parser')

        except requests.RequestException as e:
            print(f"Error fetching {url}: {e}")
            return None

    def scrape_quotes(self):
        """
        Example: Scrape quotes from quotes.toscrape.com
        """
        base_url = "http://quotes.toscrape.com"
        quotes_data = []
        page = 1

        while True:
            url = f"{base_url}/page/{page}/"
            soup = self.get_page(url)

            if not soup:
                break

            quotes = soup.find_all('div', class_='quote')

            if not quotes:
                break

            for quote in quotes:
                text = quote.find('span', class_='text').get_text()
                author = quote.find('small', class_='author').get_text()
                tags = [tag.get_text() for tag in quote.find_all('a', class_='tag')]

                quotes_data.append({
                    'text': text,
                    'author': author,
                    'tags': ', '.join(tags)
                })

            print(f"Scraped page {page}, found {len(quotes)} quotes")
            page += 1

        return quotes_data

    def scrape_news_headlines(self, url):
        """
        Generic function to scrape news headlines

        Args:
            url (str): News website URL

        Returns:
            list: List of headlines
        """
        soup = self.get_page(url)
        if not soup:
            return []

        headlines = []

        # Common selectors for headlines
        selectors = [
            'h1', 'h2', 'h3',
            '.headline', '.title',
            '[class*="headline"]', '[class*="title"]'
        ]

        for selector in selectors:
            elements = soup.select(selector)
            for element in elements:
                text = element.get_text().strip()
                if text and len(text) > 10:  # Filter out short text
                    headlines.append({
                        'headline': text,
                        'url': url,
                        'scraped_at': datetime.now().isoformat()
                    })

        return headlines

    def scrape_product_info(self, url):
        """
        Generic function to scrape product information

        Args:
            url (str): Product page URL

        Returns:
            dict: Product information
        """
        soup = self.get_page(url)
        if not soup:
            return {}

        product_info = {'url': url}

        # Try to find common product elements
        title_selectors = ['h1', '.product-title', '.title', '[class*="title"]']
        price_selectors = ['.price', '.cost', '[class*="price"]', '[class*="cost"]']
        description_selectors = ['.description', '.product-description', '[class*="description"]']

        # Extract title
        for selector in title_selectors:
            element = soup.select_one(selector)
            if element:
                product_info['title'] = element.get_text().strip()
                break

        # Extract price
        for selector in price_selectors:
            element = soup.select_one(selector)
            if element:
                product_info['price'] = element.get_text().strip()
                break

        # Extract description
        for selector in description_selectors:
            element = soup.select_one(selector)
            if element:
                product_info['description'] = element.get_text().strip()
                break

        return product_info

    def save_to_csv(self, data, filename):
        """
        Save data to CSV file

        Args:
            data (list): List of dictionaries
            filename (str): Output filename
        """
        if not data:
            print("No data to save")
            return

        df = pd.DataFrame(data)
        df.to_csv(filename, index=False)
        print(f"Data saved to {filename}")

    def save_to_json(self, data, filename):
        """
        Save data to JSON file

        Args:
            data (list): List of dictionaries
            filename (str): Output filename
        """
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        print(f"Data saved to {filename}")

def main():
    """
    Main function with examples
    """
    scraper = WebScraper(delay=1)

    print("Web Scraper Menu:")
    print("1. Scrape quotes from quotes.toscrape.com")
    print("2. Scrape news headlines from a URL")
    print("3. Scrape product information from a URL")
    print("4. Custom scraping")

    choice = input("Enter your choice (1-4): ").strip()

    if choice == '1':
        print("Scraping quotes...")
        quotes = scraper.scrape_quotes()

        if quotes:
            scraper.save_to_csv(quotes, 'quotes.csv')
            scraper.save_to_json(quotes, 'quotes.json')
            print(f"Scraped {len(quotes)} quotes successfully!")
        else:
            print("No quotes found")

    elif choice == '2':
        url = input("Enter news website URL: ").strip()
        headlines = scraper.scrape_news_headlines(url)

        if headlines:
            scraper.save_to_csv(headlines, 'headlines.csv')
            scraper.save_to_json(headlines, 'headlines.json')
            print(f"Scraped {len(headlines)} headlines successfully!")
        else:
            print("No headlines found")

    elif choice == '3':
        url = input("Enter product page URL: ").strip()
        product = scraper.scrape_product_info(url)

        if product:
            scraper.save_to_json([product], 'product.json')
            print("Product information scraped successfully!")
            print(json.dumps(product, indent=2))
        else:
            print("No product information found")

    elif choice == '4':
        print("Custom scraping example:")
        url = input("Enter URL to scrape: ").strip()

        soup = scraper.get_page(url)
        if soup:
            print("\nPage title:", soup.title.string if soup.title else "No title found")

            # Find all links
            links = soup.find_all('a', href=True)
            print(f"Found {len(links)} links")

            # Find all images
            images = soup.find_all('img', src=True)
            print(f"Found {len(images)} images")

            # You can add more custom scraping logic here
            print("Add your custom scraping logic in the code!")
        else:
            print("Failed to fetch the page")

    else:
        print("Invalid choice")

if __name__ == "__main__":
    main()