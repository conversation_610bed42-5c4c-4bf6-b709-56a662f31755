#!/usr/bin/env python3
"""
Simple example of using the WebScraper class
"""

from index import WebScraper
import json

def scrape_example_site():
    """
    Example function showing how to use the WebScraper
    """
    # Initialize scraper with 1 second delay between requests
    scraper = WebScraper(delay=1)
    
    # Example 1: Scrape quotes (built-in example)
    print("=== Scraping Quotes Example ===")
    quotes = scraper.scrape_quotes()
    if quotes:
        print(f"Found {len(quotes)} quotes")
        print("First quote:", quotes[0])
        scraper.save_to_csv(quotes, 'example_quotes.csv')
    
    # Example 2: Custom scraping
    print("\n=== Custom Scraping Example ===")
    url = "https://httpbin.org/html"  # Simple test page
    soup = scraper.get_page(url)
    
    if soup:
        # Extract all paragraph text
        paragraphs = soup.find_all('p')
        paragraph_texts = [p.get_text().strip() for p in paragraphs if p.get_text().strip()]
        
        print(f"Found {len(paragraph_texts)} paragraphs:")
        for i, text in enumerate(paragraph_texts[:3], 1):  # Show first 3
            print(f"{i}. {text}")
        
        # Save the data
        data = [{'paragraph': text, 'index': i} for i, text in enumerate(paragraph_texts, 1)]
        scraper.save_to_json(data, 'example_paragraphs.json')

def scrape_custom_site(url):
    """
    Function to scrape any website with basic information extraction
    
    Args:
        url (str): URL to scrape
    """
    scraper = WebScraper(delay=1)
    soup = scraper.get_page(url)
    
    if not soup:
        print("Failed to fetch the page")
        return
    
    # Extract basic information
    info = {
        'url': url,
        'title': soup.title.string if soup.title else "No title",
        'links_count': len(soup.find_all('a', href=True)),
        'images_count': len(soup.find_all('img', src=True)),
        'paragraphs_count': len(soup.find_all('p')),
        'headings': []
    }
    
    # Extract all headings
    for i in range(1, 7):  # h1 to h6
        headings = soup.find_all(f'h{i}')
        for heading in headings:
            text = heading.get_text().strip()
            if text:
                info['headings'].append({
                    'level': i,
                    'text': text
                })
    
    print(json.dumps(info, indent=2))
    
    # Save to file
    scraper.save_to_json([info], f'scraped_info_{url.replace("://", "_").replace("/", "_")}.json')

if __name__ == "__main__":
    print("Web Scraping Examples")
    print("1. Run built-in examples")
    print("2. Scrape a custom URL")
    
    choice = input("Choose an option (1 or 2): ").strip()
    
    if choice == '1':
        scrape_example_site()
    elif choice == '2':
        url = input("Enter URL to scrape: ").strip()
        if url:
            scrape_custom_site(url)
        else:
            print("No URL provided")
    else:
        print("Invalid choice")
